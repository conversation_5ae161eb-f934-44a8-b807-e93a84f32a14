import app from 'flarum/admin/app';
import Component from 'flarum/common/Component';
import Button from 'flarum/common/components/Button';
import LoadingIndicator from 'flarum/common/components/LoadingIndicator';
import Stream from 'flarum/common/utils/Stream';
import type {
  HeaderIconSettingComponentAttrs,
  FlarumVnode
} from '../../common/config/types';

// Constants
const SAVE_DEBOUNCE_DELAY = 500;

/**
 * Custom component for header icon URL setting with preview and file upload support
 */
export default class HeaderIconSettingComponent extends Component<HeaderIconSettingComponentAttrs> {
  private urlValue!: Stream<string>;
  private loading = false;
  private previewError = false;
  private saveTimeout?: NodeJS.Timeout;

  oninit(vnode: FlarumVnode): void {
    super.oninit(vnode);

    const settingValue = app.data.settings[this.attrs.setting] || '';
    this.urlValue = Stream(settingValue);
  }

  view(): unknown {
    const { label, help } = this.attrs;
    const currentUrl = this.urlValue();

    return m('div.Form-group', [
      m('label.FormLabel', label),
      help && m('div.helpText', help),

      m('div.HeaderIconSetting', [
        // URL Input
        m('div.HeaderIconSetting-input', [
          m('input.FormControl', {
            type: 'url',
            placeholder: 'https://example.com/icon.png',
            value: currentUrl,
            oninput: (event: Event) => {
              const target = event.target as HTMLInputElement;
              this.urlValue(target.value);
              this.previewError = false;
              this.saveValue(target.value);
            }
          })
        ]),

        // Preview
        currentUrl && m('div.HeaderIconSetting-preview', [
          m('div.HeaderIconSetting-previewLabel',
            app.translator.trans('wusong8899-client1.admin.HeaderIconPreview')
          ),
          m('div.HeaderIconSetting-previewContainer', [
            this.renderPreviewContent(currentUrl)
          ])
        ]),

        // File Upload Section (Future Enhancement)
        m('div.HeaderIconSetting-upload', [
          m('div.HeaderIconSetting-uploadLabel',
            app.translator.trans('wusong8899-client1.admin.HeaderIconUploadLabel')
          ),
          m(Button, {
            className: 'Button Button--primary',
            disabled: true,
            onclick: () => {
              // TODO: Implement file upload functionality
              app.alerts.show({
                type: 'info',
                content: app.translator.trans('wusong8899-client1.admin.HeaderIconUploadComingSoon')
              });
            }
          }, app.translator.trans('wusong8899-client1.admin.HeaderIconUploadButton')),
          m('div.HeaderIconSetting-uploadHelp',
            app.translator.trans('wusong8899-client1.admin.HeaderIconUploadHelp')
          )
        ])
      ])
    ]);
  }

  /**
   * Render preview content based on loading and error states
   */
  private renderPreviewContent(currentUrl: string): unknown {
    if (this.loading) {
      return m(LoadingIndicator, { size: 'small' });
    }

    if (this.previewError) {
      return m('div.HeaderIconSetting-previewError',
        app.translator.trans('wusong8899-client1.admin.HeaderIconPreviewError')
      );
    }

    return m('img.HeaderIconSetting-previewImage', {
      src: currentUrl,
      alt: 'Header Icon Preview',
      onload: () => {
        this.previewError = false;
        m.redraw();
      },
      onerror: () => {
        this.previewError = true;
        m.redraw();
      }
    });
  }

  /**
   * Save the setting value
   */
  private saveValue(value: string): void {
    // Debounce the save operation
    clearTimeout(this.saveTimeout);
    this.saveTimeout = setTimeout(() => {
      app.data.settings[this.attrs.setting] = value;

      // Save to backend
      app.request({
        method: 'POST',
        url: app.forum.attribute('apiUrl') + '/settings',
        body: {
          [this.attrs.setting]: value
        }
      }).catch(() => {
        // Handle save error silently for now
      });
    }, SAVE_DEBOUNCE_DELAY);
  }
}
